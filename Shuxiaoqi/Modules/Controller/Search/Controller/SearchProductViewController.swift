//搜索-商品
import UIKit
import SnapKit
import Kingfisher

class SearchProductViewController: UIViewController, Searchable {
    // MARK: - 数据模型
    private var products: [RecommendedGoodItem] = []

    // MARK: - UI
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 8
        layout.minimumLineSpacing = 10
        let itemWidth = (UIScreen.main.bounds.width - 12 * 2 - 8) / 2
        layout.itemSize = CGSize(width: itemWidth, height: 270)
        layout.sectionInset = UIEdgeInsets(top: 0, left: 12, bottom: 12, right: 12)
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = UIColor(hex: "#F5F5F5")
        cv.delegate = self
        cv.dataSource = self
        cv.register(DiscoverProductCell.self, forCellWithReuseIdentifier: "DiscoverProductCell")
        return cv
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        // 初次不加载数据，等待关键词
    }

    // MARK: - Searchable 协议实现
    func search(with keyword: String) {
        guard !keyword.isEmpty else { return }
        let request = EcommerceProductSearchRequest(keywords: keyword, page: 0)
        APIManager.shared.searchEcommerceProduct(request: request) { [weak self] result in
            switch result {
            case .success(let resp):
                guard resp.status == 200 || resp.status == 1 else { print("商品搜索失败: \(resp.errMsg)"); return }
                let items = resp.data.data
                let mapped: [RecommendedGoodItem] = items.map { item in
                    var good = RecommendedGoodItem()
                    good.goodId = item.goodId ?? item.goodsId.flatMap { Int($0) }
                    good.goodsId = item.goodsId ?? String(item.goodId ?? 0)
                    good.goodName = item.goodName
                    good.tag = "厂家直供"
                    good.swiperImageList = item.swiperImageList
                    good.sales = item.sales
                    good.goodPriceRange = GoodPriceRange(min: item.goodPriceRange?.min, max: item.goodPriceRange?.max)
                    return good
                }
                DispatchQueue.main.async {
                    self?.products = mapped
                    self?.collectionView.reloadData()
                }
            case .failure(let error):
                print("网络错误: \(error)")
            }
        }
    }
}

extension SearchProductViewController: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int { products.count }
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "DiscoverProductCell", for: indexPath) as! DiscoverProductCell
        let item = products[indexPath.item]
        // DiscoverProductCell已有自定义方法 configureWithRecommendedItem
        cell.configureWithRecommendedItem(item)
        return cell
    }
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // TODO: push detail
    }
}


//curl --request GET \
//  --url https://test-youshu.gzyoushu.com/video/api/goods/v1/searchGood.do \
//  --header 'Accept: */*' \
//  --header 'Accept-Encoding: gzip, deflate, br' \
//  --header 'Authorization: eyJhbGciOiJIUzUxMiJ9.eyJzZXNzaW9uS2V5IjoieXNfdmlkZW8iLCJzdWIiOiJmZjgwODA4MTk2NzRjYmMwMDE5Njc0ZDE1OWYwMDAwMSIsImlhdCI6MTc1MDUxMDUyMiwiZXhwIjoxNzU0MTEwNTIyfQ.2QhN2no7bx-lUJM26k49hUdOdphW0t3DCNDy9mweCIROcFY1R4UF7C38wIhBbgzm-JNHGgUna9otOuwn-eAnEg' \
//  --header 'Connection: keep-alive' \
//  --header 'Cookie: JSESSIONID=06DFD0540EAF4DA1331A7F290F0ABE93' \
//  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
//  --header 'content-type: multipart/form-data' \
//  --form page=0 \
//  --form searchType=good \
//  --form size=10 \
//  --form keywords=
