//
//  SceneDelegate.swift
//  Shuxiaoqi
//
//  Created by steamed_b on 2025/3/18.
//

import UIKit

class SceneDelegate: UIResponder, UIWindowSceneDelegate {

    var window: UIWindow?

    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        guard let windowScene = (scene as? UIWindowScene) else { return }

        // 确保只创建一次窗口
        window = UIWindow(windowScene: windowScene)

        // 立即显示窗口，减少启动页显示时间
        window?.makeKeyAndVisible()

        // 延后执行界面创建，让启动页快速消失
        DispatchQueue.main.async {
            self.setupInitialInterface()
        }
    }

    /// 设置初始界面
    private func setupInitialInterface() {
        // 检查是否首次安装
        if UserDefaults.standard.bool(forKey: "hasLaunchedBefore") {
            // 非首次安装，直接进入主界面
            setupMainInterface()
        } else {
            // 首次安装，显示引导页
            let guideVC = GuideViewController()
            window?.rootViewController = guideVC

            // 标记已经启动过应用
            UserDefaults.standard.set(true, forKey: "hasLaunchedBefore")
            UserDefaults.standard.synchronize()
        }
    }
    
    func scene(_ scene: UIScene, openURLContexts URLContexts: Set<UIOpenURLContext>) {
        if let url = URLContexts.first?.url {
            WXApi.handleOpen(url, delegate: UIApplication.shared.delegate as? WXApiDelegate)
        }
    }
    
    // 设置主界面的方法
    func setupMainInterface() {
        // 创建自定义 TabBar 控制器
        let tabBarController = CustomTabBarController()

        // 延迟加载策略：先创建轻量级的占位控制器，实际控制器在需要时再创建
        let homeNav = createLazyNavigationController(for: .home)
        let friendsNav = createLazyNavigationController(for: .friends)
        let addNav = createLazyNavigationController(for: .add)
        let messageNav = createLazyNavigationController(for: .message)
        let meNav = createLazyNavigationController(for: .me)

        // 设置视图控制器
        tabBarController.setViewControllers([homeNav, friendsNav, addNav, messageNav, meNav], animated: false)

        // 设置窗口的根视图控制器
        window?.rootViewController = tabBarController

        // 确保TabBar显示并选择首页
        tabBarController.showTabBar()
        tabBarController.selectTab(at: 0) // 确保选择第一个标签
    }

    /// 创建延迟加载的导航控制器
    private func createLazyNavigationController(for tab: TabType) -> HiddenNavController {
        // 先创建一个轻量级的占位控制器
        let placeholderVC = UIViewController()
        placeholderVC.view.backgroundColor = .white

        switch tab {
        case .home:
            placeholderVC.title = "首页"
            // 在需要时再创建真正的HomeViewController
            DispatchQueue.main.async {
                let homeVC = HomeViewController()
                homeVC.title = "首页"
                homeVC.isTabBarRootViewController = true
                let nav = placeholderVC.navigationController as? HiddenNavController
                nav?.setViewControllers([homeVC], animated: false)
            }
        case .friends:
            placeholderVC.title = "朋友"
        case .add:
            placeholderVC.title = "发布"
        case .message:
            placeholderVC.title = "消息"
        case .me:
            placeholderVC.title = "我的"
        }

        return HiddenNavController(rootViewController: placeholderVC)
    }

    enum TabType {
        case home, friends, add, message, me
    }

    func sceneDidDisconnect(_ scene: UIScene) {
        // Called as the scene is being released by the system.
        // This occurs shortly after the scene enters the background, or when its session is discarded.
        // Release any resources associated with this scene that can be re-created the next time the scene connects.
        // The scene may re-connect later, as its session was not necessarily discarded (see `application:didDiscardSceneSessions` instead).
    }

    func sceneDidBecomeActive(_ scene: UIScene) {
        // Called when the scene has moved from an inactive state to an active state.
        // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
        checkClipboardForAddFriendCommand()
    }

    func sceneWillResignActive(_ scene: UIScene) {
        // Called when the scene will move from an active state to an inactive state.
        // This may occur due to temporary interruptions (ex. an incoming phone call).
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
        // Called as the scene transitions from the background to the foreground.
        // Use this method to undo the changes made on entering the background.
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
        // Called as the scene transitions from the foreground to the background.
        // Use this method to save data, release shared resources, and store enough scene-specific state information
        // to restore the scene back to its current state.
    }

    // MARK: - Clipboard Command Handling
    private func checkClipboardForAddFriendCommand() {
        // 口令前缀
        let prefix = "复制到【树小柒】APP添加"

        guard let clipboardText = UIPasteboard.general.string,
              clipboardText.hasPrefix(prefix) else { return }

        // 防止重复弹窗
        if UserDefaults.standard.string(forKey: "lastProcessedClipboard") == clipboardText {
            return
        }

        let userId = clipboardText.replacingOccurrences(of: prefix, with: "").trimmingCharacters(in: .whitespacesAndNewlines)
        guard !userId.isEmpty else { return }

        // 保存已处理口令
        UserDefaults.standard.set(clipboardText, forKey: "lastProcessedClipboard")

        // 构建提示弹窗
        let alert = UIAlertController(title: "添加好友", message: "检测到好友口令，是否查看用户 \(userId)？", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
        alert.addAction(UIAlertAction(title: "查看", style: .default) { _ in
            // 跳转到个人主页
            let personalVC = PersonalHomepageViewController()
            personalVC.userId = userId
            // 寻找当前最顶层视图控制器
            if let topVC = self.topViewController(base: self.window?.rootViewController) {
                if let nav = topVC.navigationController {
                    nav.pushViewController(personalVC, animated: true)
                } else {
                    topVC.present(personalVC, animated: true)
                }
            }
        })

        // 显示弹窗
        DispatchQueue.main.async {
            if let topVC = self.topViewController(base: self.window?.rootViewController) {
                topVC.present(alert, animated: true)
            }
        }
    }

    private func topViewController(base: UIViewController?) -> UIViewController? {
        if let nav = base as? UINavigationController {
            return topViewController(base: nav.visibleViewController)
        } else if let tab = base as? UITabBarController,
                  let selected = tab.selectedViewController {
            return topViewController(base: selected)
        } else if let presented = base?.presentedViewController {
            return topViewController(base: presented)
        }
        return base
    }

}

